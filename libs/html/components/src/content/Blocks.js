import React from 'react';
import { colors } from '@guryou/colors';
import { borderDefinition, boxShadowLight, sizes, smallBorderRadius, spacer } from '@guryou/html-theme';
import { AppBar, Toolbar } from '@material-ui/core';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import propsToCss, { toPixel } from '../utils/propsToCss';

const Block = styled.div`
  overflow-y: hidden;
  margin: 0 auto;
  ${props => propsToCss(props)};
`;

const PaperBlock = styled.div`
  background-color: ${colors.white};
  padding: 20px;
  box-shadow: ${boxShadowLight};
  border-radius: ${smallBorderRadius};
  border: ${borderDefinition()};
  margin: 0 auto;
  ${props => propsToCss(props)};
`;

const FlexColumn = styled(Block)`
  display: flex;
  flex-direction: column;
  align-items: center;
  ${({ hover, hoverEffect }) =>
    hover &&
    `&:hover {
    ${Object.keys(hoverEffect)
      .map(x => `${x}: ${hoverEffect[x]};`)
      .join('')}
  }`}
`;

const FlexRow = styled(Block)`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  overflow: unset;
`;

const GyAppBar = ({ children, minHeight, height, marginTop, ...props }) => {
  const styles = { minHeight: toPixel(minHeight), height: toPixel(height), marginTop };
  return (
    <AppBar style={styles} {...props}>
      {children}
    </AppBar>
  );
};

const GyToolbar = ({ children, minHeight, justifyContent, ...props }) => {
  const styles = { minHeight: toPixel(minHeight), justifyContent };

  return (
    <Toolbar style={styles} {...props}>
      {children}
    </Toolbar>
  );
};

// Obsolete
const CenterMediumContent = styled.div`
  width: ${sizes.medium}px;
  padding: ${({ isMobile }) => (isMobile ? '0' : `${spacer.spacer2}px`)}
  margin: ${spacer.spacer3}px auto;
  margin-top: ${props => `${(props.isPublicContent ? -150 : 0) + spacer.spacer3}px`};
`;

const LittleCircle = styled.div`
  width: 10px;
  height: 10px;
  border-radius: 50px;
  background: '#fff';
  border: '1px solid #000';
  ${props => propsToCss(props)};
`;

const VerticalLine = styled.div`
  width: 1px;
  background: #252525;
  ${props => propsToCss(props)};
`;

Block.propTypes = {
  margin: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  hover: PropTypes.object,
};

Block.defaultProps = {
  padding: 0,
  margin: 1,
  xs: 12,
};

FlexRow.defaultProps = {
  padding: 0,
  margin: 1,
  xs: 12,
};

export { PaperBlock, Block, FlexColumn, FlexRow, GyAppBar as AppBar, GyToolbar as Toolbar, CenterMediumContent, LittleCircle, VerticalLine };
