import React, { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import {
  Block,
  closeModal,
  ContainedButton,
  DefaultText,
  DeleteIcon,
  DropDownIcon,
  DropUpIcon,
  EditIcon,
  ExpandLessIcon,
  ExpandMoreIcon,
  FlexColumn,
  FlexRow,
  Form,
  HiddenField,
  IconButton,
  NumberBox,
  openModal,
  SelectOption,
  SmallText,
} from '@guryou/html-components';
import { borderDefinition } from '@guryou/html-theme';
import { useExerciseDisplayUnits, useExerciseMode } from '@guryou/redux';
import { parseAsBool } from '@guryou/utils';
import EquipmentForm from './EquipmentForm';
import ExerciseSelectionModal from './ExerciseSelectionModal';
import { WorkoutContext } from './WorkoutContext';

const defaultState = {
  mode: null,
  units: null,
  repetitions: {},
  executionMaxValue: {},
  executionMinValue: {},
};

const CustomExerciseForm = ({ circuitIndex, exerciseIndex, circuitId }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [ls, setLs] = useState(defaultState);
  const exerciseModes = useExerciseMode();
  const exerciseDisplayUnits = useExerciseDisplayUnits();
  const { state, removeExercise, saveExercise, saveEquipment, reorderExercises, toggleExpandedState, filterUnitsByMode } = useContext(WorkoutContext);
  const circuit = state.workout.circuits[circuitIndex];
  const item = circuit.exercises[exerciseIndex];
  const modalId = `exercise-selection-${circuitId}-${item.id}`;
  const [isEditing, setIsEditing] = useState(false);
  const [selectedExercise, setSelectedExercise] = useState(item.exercise);

  useEffect(() => {
    if (item && exerciseDisplayUnits && exerciseDisplayUnits.length) {
      const units = filterUnitsByMode(item.executionMode, exerciseDisplayUnits);
      setLs(pv => ({ ...pv, units }));
    }
  }, [exerciseDisplayUnits, filterUnitsByMode, item]);

  useEffect(() => {
    if (ls?.mode) {
      const units = filterUnitsByMode(ls.mode, exerciseDisplayUnits);
      setLs(pv => ({ ...pv, units }));
    }
    //eslint-disable-next-line
  }, [exerciseDisplayUnits, ls.mode]);

  // Sync selectedExercise with item.exercise when it changes
  useEffect(() => {
    setSelectedExercise(item.exercise);
  }, [item.exercise]);

  const handleModeChange = mode => setLs(pv => ({ ...pv, mode }));

  const handleChange = values => {
    const repsCheck = !!values.repetitions && +values.repetitions <= 0;
    const maxCheck = !!values.executionMaxValue && +values.executionMaxValue <= 0;
    const minCheck = !!values.executionMinValue && +values.executionMinValue < 0;
    const maxBiggerThanMinCheck = !!values.executionMaxValue && !!values.executionMinValue && +values.executionMaxValue < +values.executionMinValue;
    const message = t('GENERAL.valueMustBeGreaterThan0');
    const message2 = t('GENERAL.maxBiggerThanMin');
    if (repsCheck) {
      setLs(pv => ({ ...pv, repetitions: { message, version: `reps_${Date.now()}`, value: values.repetitions } }));
    }
    if (maxCheck) {
      setLs(pv => ({
        ...pv,
        executionMaxValue: { message, version: `max_${Date.now()}`, value: values.executionMaxValue },
      }));
    }
    if (minCheck) {
      setLs(pv => ({
        ...pv,
        executionMinValue: { message, version: `min_${Date.now()}`, value: values.executionMinValue },
      }));
    }
    if (maxBiggerThanMinCheck) {
      setLs(pv => ({
        ...pv,
        executionMinValue: { message: message2, version: `min_${Date.now()}`, value: values.executionMinValue },
      }));
    }
    if (repsCheck || maxCheck || minCheck || maxBiggerThanMinCheck) {
      return;
    } else {
      setLs(pv => ({
        ...pv,
        repetitions: { message: '', version: `reps_${Date.now()}`, value: values.repetitions },
        executionMaxValue: { message: '', version: `max_${Date.now()}`, value: values.executionMaxValue },
        executionMinValue: { message: '', version: `min_${Date.now()}`, value: values.executionMinValue },
      }));
    }
    saveExercise(values);
  };

  const handleOpenExerciseModal = () => {
    dispatch(openModal(modalId, { title: t('EXERCISE.exercises') }));
  };

  const handleExerciseSelect = exercise => {
    // Update the local state immediately for instant UI feedback
    setSelectedExercise(exercise);

    // Create the values object that handleChange expects
    const values = {
      id: item.id,
      circuitId: circuitId,
      idx: item.idx,
      exerciseId: exercise.id,
      // Include current form values to maintain them
      repetitions: item.repetitions,
      executionMaxValue: item.executionMaxValue,
      executionMinValue: item.executionMinValue,
      executionMode: item.executionMode,
      executionUnit: item.executionUnit,
    };

    // Call handleChange which will validate and save
    handleChange(values);

    dispatch(closeModal(modalId));
  };

  return (
    <FlexRow padding={1} border={borderDefinition()}>
      <FlexRow xs={12} alignItems="center" marginBottom={2}>
        {/* Image */}
        <Block xs={12} md={2} margin={1} display="flex" alignItems="center" justifyContent="center">
          {item.exercise?.gifThumbnailUrl ? (
            <img
              src={item.exercise.gifThumbnailUrl}
              alt={item.exercise.name || 'Exercise'}
              style={{ maxHeight: '120px', maxWidth: '100%', objectFit: 'contain' }}
            />
          ) : item.exercise?.imageThumbnailUrl ? (
            <img
              src={item.exercise.imageThumbnailUrl}
              alt={item.exercise.name || 'Exercise'}
              style={{ maxHeight: '120px', maxWidth: '100%', objectFit: 'contain' }}
            />
          ) : (
            <Block height="120px" display="flex" alignItems="center" justifyContent="center">
              <SmallText>No image available</SmallText>
            </Block>
          )}
        </Block>
        {/* Title */}
        <FlexColumn xs={12} md={6} margin={1} display="flex" justifyContent="center">
          <DefaultText fontWeight="bold" style={{ marginBottom: '5px' }}>
            {item.exercise?.name || 'Exercise'}
          </DefaultText>
          {item.executionMode === 'duration' ? (
            <DefaultText>
              {item.repetitions} set(s) of{' '}
              {(() => {
                const minValue = ls.executionMinValue?.value || item.executionMinValue;
                const maxValue = ls.executionMaxValue?.value || item.executionMaxValue;
                const units = item.executionDisplayUnits || 'second(s)';

                if (minValue && maxValue) {
                  return `${minValue}-${maxValue} ${units}`;
                } else if (maxValue) {
                  return `${maxValue} ${units}`;
                } else {
                  return units;
                }
              })()}
            </DefaultText>
          ) : (
            <DefaultText>
              {item.repetitions} set(s) of{' '}
              {(() => {
                const minValue = ls.executionMinValue?.value || item.executionMinValue;
                const maxValue = ls.executionMaxValue?.value || item.executionMaxValue;
                const units = item.executionDisplayUnits || '';

                if (minValue && maxValue) {
                  return `${minValue}-${maxValue} ${units}`.trim();
                } else if (maxValue) {
                  return `${maxValue} ${units}`.trim();
                } else {
                  return 'reps';
                }
              })()}
            </DefaultText>
          )}
        </FlexColumn>
        {/* Buttons */}
        <Block xs={12} md={4} margin={0} marginLeft="auto" display="flex" alignItems="center" justifyContent="flex-end">
          <IconButton icon={DeleteIcon} color="secondary" onClick={() => removeExercise(item)} />
          <IconButton
            icon={state.expanded[item.id] ? DropUpIcon : DropDownIcon}
            color="primary"
            onClick={e => {
              e.stopPropagation();
              toggleExpandedState(item.id);
            }}
          />
          <IconButton
            icon={EditIcon}
            color="primary"
            onClick={() => setIsEditing(v => !v)}
            title={isEditing ? t('GENERAL.cancel') : t('GENERAL.edit')}
          />
          <FlexColumn margin="0" width="auto">
            <IconButton icon={ExpandLessIcon} onClick={() => reorderExercises(circuitIndex, exerciseIndex, exerciseIndex - 1)} padding={1} />
            <IconButton icon={ExpandMoreIcon} onClick={() => reorderExercises(circuitIndex, exerciseIndex, exerciseIndex + 1)} padding={1} />
          </FlexColumn>
        </Block>
      </FlexRow>

      {/* Form fields */}
      {isEditing ? (
        <Form actions={false} parserVersion={2} enableDebounce={true} onChange={handleChange} debounceTimeout={2000}>
          <FlexRow alignItems="center" marginTop={0} marginBottom={0}>
            <HiddenField name="id" value={item.id} />
            <HiddenField name="circuitId" value={circuitId} />
            <HiddenField name="idx" value={item.idx} />
            <Block xs={12} md={3} margin={2}>
              <FlexColumn>
                <SmallText marginBottom={0.5} color="textSecondary">
                  {t('EXERCISE.title')}
                </SmallText>
                <ContainedButton
                  key={`exercise-button-${selectedExercise?.id || 'none'}`}
                  text={selectedExercise?.name || t('EXERCISE.exercises')}
                  onClick={handleOpenExerciseModal}
                  variant="outlined"
                  fullWidth
                  style={{
                    textAlign: 'left',
                    justifyContent: 'flex-start',
                    textTransform: 'none',
                    padding: '8px 12px',
                  }}
                />
                <HiddenField key={`exercise-id-${selectedExercise?.id || 'none'}`} name="exerciseId" value={selectedExercise?.id || ''} />
              </FlexColumn>
            </Block>
            <NumberBox
              key={ls.repetitions?.version}
              name="repetitions"
              value={ls.repetitions?.value || item.repetitions}
              label={t('EXERCISE.repetitions')}
              xs={12}
              md={1}
              inputProps={{ min: 1, step: 1 }}
              margin={2}
              error={ls.repetitions?.message}
              forceError={ls.repetitions?.message}
            />
            <SelectOption
              name="executionMode"
              xs={12}
              md={2}
              onChange={handleModeChange}
              value={item.executionMode}
              options={exerciseModes}
              label={t('WORKOUTS.executionMode')}
              margin={2}
              none={false}
            />
            <NumberBox
              key={`${ls.executionMaxValue?.version}_${item.executionMaxValue}`}
              name="executionMaxValue"
              xs={12}
              md={1}
              value={ls.executionMaxValue?.value || item.executionMaxValue}
              label={t('WORKOUTS.executionMaxValue')}
              inputProps={{ min: 1, step: 1 }}
              margin={2}
              error={ls.executionMaxValue?.message}
              forceError={ls.executionMaxValue?.message}
            />
            <NumberBox
              key={ls.executionMinValue?.version}
              name="executionMinValue"
              xs={12}
              md={1}
              value={ls.executionMinValue?.value || item.executionMinValue}
              label={t('WORKOUTS.executionMinValue')}
              inputProps={{ min: 0, step: 1 }}
              margin={2}
              error={ls.executionMinValue?.message}
              forceError={ls.executionMinValue?.message}
            />
            <SelectOption
              key={item.executionDisplayUnits}
              xs={12}
              md={1.5}
              name="executionDisplayUnits"
              value={item.executionDisplayUnits}
              options={ls?.units || exerciseDisplayUnits}
              label={t('WORKOUTS.executionDisplayUnits')}
              margin={2}
              none={false}
            />
            <NumberBox name="restTime" xs={12} md={1} value={item.restTime ?? 20} label={t('WORKOUTS.restTime')} margin={2} />
          </FlexRow>
        </Form>
      ) : null}

      {/* Equipment section - only shown when expanded */}
      {state.expanded[item.id] && (
        <FlexRow>
          <ContainedButton
            text={t('EQUIPMENT.addEquipment')}
            marginLeft="auto"
            onClick={() => saveEquipment({ circuitId: circuit.id, workoutCircuitId: item.id })}
          />
          {item.exercise.equipments
            ?.sort((a, b) => (a.isDefault === b.isDefault ? a.name?.localeCompare(b.name || '') : parseAsBool(a.isDefault) ? -1 : 1))
            ?.map((_, equipmentIdx) => (
              <EquipmentForm
                key={`equipment_form_${circuitIndex}_${exerciseIndex}_${equipmentIdx}`}
                circuitIndex={circuitIndex}
                exerciseIndex={exerciseIndex}
                equipmentIndex={equipmentIdx}
              />
            ))}
        </FlexRow>
      )}

      {/* Exercise Selection Modal */}
      <ExerciseSelectionModal modalId={modalId} onSelect={handleExerciseSelect} selectedExercise={item.exercise} title={t('EXERCISE.exercises')} />
    </FlexRow>
  );
};

export default CustomExerciseForm;
