import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { SubscriptionPackages } from '@guryou/core';
import {
  AddSimpleIcon,
  ContainedButton,
  EditIcon,
  EmptyPage,
  FileIcon,
  Filters,
  IconTextButton,
  LargeText,
  Modal,
  Modals,
  MultipleItemsContainer,
  openModal,
  TableCellBody,
  TableRowBody,
  TextBox,
  ToolbarActions,
  ToolTipHover,
} from '@guryou/html-components';
import { deleteWorkout, duplicateWorkout, getWorkouts, stateIsLoaded, useEffectOnSuccess } from '@guryou/redux';
import { checkIfCollaborator, printDate } from '@guryou/utils';
import { resetModal } from 'services/actions';
import ForbiddenAccess from 'components/ForbiddenAccess';
import RedirectToPackages from 'components/RedirectToPackages';
import CreateWorkout from './CreateWorkout';
import ServiceActions from '../ServiceActions';

const Workouts = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const history = useHistory();

  const workouts = useSelector(state => state.workouts.all);
  const authService = useSelector(state => state.auth.service);
  const generalInfo = useSelector(state => state.provider.generalInfo);

  useEffect(() => {
    dispatch(getWorkouts());
  }, [dispatch]);

  useEffectOnSuccess(
    state => state.workouts.create,
    data => {
      dispatch(resetModal());
      history.push(`workouts/${data.id}`);
    }
  );

  useEffectOnSuccess([state => state.workouts.status, state => state.workouts.delete, state => state.workouts.duplicate], () => {
    dispatch(getWorkouts(true));
  });

  const onFiltersChange = (data, items) => {
    if (!data || !items) {
      return items;
    }

    let result = items;

    if (data.search) {
      const searchValue = data.search.toLowerCase();
      result = result.filter(x => x.title && x.title.toLowerCase().includes(searchValue));
    }
    return result;
  };

  if (stateIsLoaded(generalInfo) && (!generalInfo.data?.plan || generalInfo.data?.plan?.id <= SubscriptionPackages.grow)) {
    if (checkIfCollaborator()) {
      return <ForbiddenAccess />;
    }
    return <RedirectToPackages />;
  }

  return (
    <MultipleItemsContainer title={t('WORKOUTS.titlePlural')} {...workouts}>
      <EmptyPage>
        <LargeText>{t('WORKOUTS.noWorkouts')}</LargeText>
        <ContainedButton color="primary" label="+Workout" onClick={() => dispatch(openModal('workoutCreate'))} text={t('WORKOUTS.addWorkout')} />
      </EmptyPage>
      <Filters onChange={onFiltersChange}>
        <TextBox name="search" label={t('GENERAL.search')} placeholder={t('GENERAL.title')} />
      </Filters>
      <TableRowBody>
        <TableCellBody name="id" display={false} />
        <TableCellBody name="title" label={t('GENERAL.title')} />
        <TableCellBody
          name="description"
          label={t('GENERAL.description')}
          render={value => <ToolTipHover textAlign="left" label={value} renderDots={true} text={value} />}
        />
        <TableCellBody name="level" label={t('GENERAL.level')} />
        <TableCellBody name="dateCreated" label={t('GENERAL.createdAt')} render={value => printDate(value)} />
        {/* <TableCellBody
          name="active"
          label={t('GENERAL.status')}
          size="small"
          render={(value, meta) => (
            <SwitchBox
              disabled={checkIfCollaborator()}
              color="default"
              onClick={e => {
                e.stopPropagation();
                dispatch(toggleWorkoutStatus(meta.rowData[0], !value));
              }}
              checked={value}
            />
          )}
        /> */}
        <TableCellBody
          name="id"
          label={t('GENERAL.actions')}
          size="small"
          render={value => (
            <ServiceActions
              edit={{
                func: () => history.push(`workouts/${value}`),
                disabled: checkIfCollaborator(),
                label: t('GENERAL.edit'),
              }}
              extra2={{
                icon: <EditIcon />,
                func: () => history.push(`/providers/${authService?.providerId}/workouts/edit/${value}`),
                disabled: checkIfCollaborator(),
                label: t('EditV2'),
              }}
              archive={{
                func: () => dispatch(deleteWorkout(value)),
                disabled: checkIfCollaborator(),
                label: t('GENERAL.delete'),
              }}
              create={{
                func: () => dispatch(duplicateWorkout(value)),
                disabled: checkIfCollaborator(),
                label: t('GENERAL.duplicate'),
                icon: <FileIcon />,
              }}
            />
          )}
        />
      </TableRowBody>
      <ToolbarActions>
        <IconTextButton onClick={() => dispatch(openModal('workoutCreate'))} text={t('WORKOUTS.title')} icon={<AddSimpleIcon />} />
      </ToolbarActions>
      <Modals>
        <Modal id="workoutCreate" title={t('WORKOUTS.createNewWorkout')} maxWidth="sm">
          <CreateWorkout />
        </Modal>
      </Modals>
    </MultipleItemsContainer>
  );
};

export default Workouts;
