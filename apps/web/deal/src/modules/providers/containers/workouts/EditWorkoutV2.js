// import React, { useEffect, useState } from 'react';
// import { useTranslation } from 'react-i18next';
// import { useDispatch, useSelector } from 'react-redux';
// import { useParams } from 'react-router-dom';
// import {
//   CheckBox,
//   ContainedButton,
//   EmptyPage,
//   Filters,
//   FlexColumn,
//   FlexRow,
//   Form,
//   LargeText,
//   MultipleItemsContainer,
//   NumberBox,
//   OutlinedButton,
//   PaperBlock,
//   SelectOption,
//   TableCellBody,
//   TableRowBody,
//   TextAreaBox,
//   TextBox,
//   ToolbarActions,
// } from '@guryou/html-components';
// import { EntityTypes, getWorkoutDetails, updateWorkout, useGroups, useSharedMedia, WORKOUT_DETAILS } from '@guryou/redux';
// import { parseAsBool } from '@guryou/utils';

// const defaultState = {
//   chooseFile: false,
//   file: null,
// };

// const EditWorkoutV2 = () => {
//   const { id } = useParams();
//   const { t } = useTranslation();
//   const dispatch = useDispatch();

//   const [ls, setLs] = useState(defaultState);
//   const [filterText, setFilterText] = useState('');
//   const [isLoading, setIsLoading] = useState(true);

//   const workoutUpdate = useSelector(state => state.workouts.updateWorkout);
//   const workoutDetails = useSelector(state => state.workouts.workoutDetails);

//   const media = useSharedMedia();
//   const groups = useGroups({ type: EntityTypes.workout });

//   useEffect(() => {
//     if (id) {
//       dispatch(getWorkoutDetails(id));
//     }
//   }, [dispatch, id]);

//   useEffect(() => {
//     if (workoutDetails?.data) {
//       setIsLoading(false);
//     }
//   }, [workoutDetails]);

//   const handleSubmit = values => {
//     const data = { ...values };
//     if (id) {
//       dispatch(updateWorkout(data));
//       return;
//     }
//   };

//   const onFilterChange = (data, items) => {
//     if (!data || !items) {
//       return items;
//     }

//     let result = items;

//     if (data.search) {
//       const searchValue = data.search.toLowerCase();
//       result = result.filter(x => x.title && x.title.toLowerCase().includes(searchValue));
//     }
//     return result;
//   };

//   if (isLoading) {
//     return <div>Loading...</div>;
//   }

//   return (
//     <Form
//       actions={false}
//       parserVersion={2}
//       enableDebounce={true}
//       onChange={handleSubmit}
//       debounceTimeout={2000}
//       key={`workout_${workoutDetails?.data?.id || 'new'}`}
//     >
//       <FlexRow alignItems="stretch">
//         <PaperBlock xs={3.9} display="flex" flexWrap="wrap" marginBottom={1}>
//           <TextBox name="title" label={t('GENERAL.title')} value={workoutDetails?.data?.title || ''} required />
//           <SelectOption
//             none={false}
//             circle={true}
//             required
//             label={t('GENERAL.level')}
//             name="level"
//             options={[
//               { id: 'Beginner', label: t('GENERAL.beginner') },
//               { id: 'Intermediate', label: t('GENERAL.intermediate') },
//               { id: 'Advanced', label: t('GENERAL.advanced') },
//             ]}
//             value={workoutDetails?.data?.level || ''}
//           />
//           <SelectOption
//             none={false}
//             circle={true}
//             label={t('COURSES.selectGroup')}
//             name="workoutGroupId"
//             options={groups.data.map(x => ({ id: x.id, label: `${x.name} (${x.counter})`, color: x.color }))}
//             value={workoutDetails?.data?.workoutGroupId || ''}
//           />
//         </PaperBlock>
//         <FlexColumn xs={3.9} marginBottom={1}>
//           <PaperBlock xs={12} display="flex" flexWrap="wrap" marginBottom={1}>
//             <TextAreaBox name="description" label={t('GENERAL.description')} value={workoutDetails?.data?.description || ''} />
//           </PaperBlock>
//           <PaperBlock xs={12} display="flex" flexWrap="wrap" marginBottom={1} padding={1}>
//             <CheckBox name="active" label={t('GENERAL.active')} value="Y" checked={workoutDetails?.data?.active || false} />
//           </PaperBlock>
//         </FlexColumn>
//         <PaperBlock xs={3.9} display="flex" flexWrap="wrap" marginBottom={1} justifyContent="center" alignItems="center" padding={1}>
//           <OutlinedButton onClick={() => setLs(pv => ({ ...pv, chooseFile: true }))}>{t('MEDIA_GALLERY.browse')}</OutlinedButton>
//           {workoutDetails?.data?.image && (
//             <img
//               src={workoutDetails.data.image.thumbnailUrl}
//               alt="Workout"
//               style={{ maxWidth: '100%', maxHeight: '200px', objectFit: 'contain', marginTop: '10px' }}
//             />
//           )}
//         </PaperBlock>
//       </FlexRow>

//       <PaperBlock xs={12} marginTop={2}>
//         <MultipleItemsContainer data={workoutDetails?.data?.circuits || []} title={t('WORKOUTS.circuits')}>
//           <Filters onChange={onFilterChange}>
//             <TextBox label={t('GENERAL.search')} margin="none" name="search" placeholder={t('GENERAL.enterName')} />
//           </Filters>
//           <EmptyPage>
//             <LargeText>{t('GENERAL.emptyPage')}</LargeText>
//           </EmptyPage>
//           <TableRowBody>
//             <TableCellBody name="id" display={false} />
//             <TableCellBody name="title" label={t('GENERAL.name')} />
//             <TableCellBody name="description" label={t('GENERAL.description')} />
//             <TableCellBody name="exercises" label={t('EXERCISE.titlePlural')} render={value => value?.length || 0} />
//           </TableRowBody>
//           <ToolbarActions>
//             <ContainedButton text={t('GENERAL.save')} color="primary" type="submit" />
//           </ToolbarActions>
//         </MultipleItemsContainer>
//       </PaperBlock>
//     </Form>
//   );
// };

// export default EditWorkoutV2;
