import React from 'react';
import { useParams } from 'react-router-dom';
import { WorkoutProvider } from './WorkoutContext';
import WorkoutForm from './WorkoutForm';

const EditWorkoutV2 = () => {
  const { workoutId } = useParams();

  if (!workoutId) {
    return <div>No workout ID found in URL</div>;
  }

  return (
    <WorkoutProvider>
      <WorkoutForm customMode={true} />
    </WorkoutProvider>
  );
};

export default EditWorkoutV2;
