import React, { createContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { ExerciseDisplayUnits, ExerciseExecutionMode } from '@guryou/core';
import {
  createCircuit,
  createCircuitExercise,
  createCircuitExerciseEquipment,
  deleteWorkoutEquipmentOverrideItem,
  deleteWorkoutItem,
  getEquipments,
  getExercises,
  getWorkoutDetails,
  reorderCircuitItems,
  resetState,
  stateIsLoaded,
  StateStatus,
  updateCircuit,
  updateCircuitExercise,
  updateCircuitExerciseEquipment,
  useEffectOnSuccess,
  WORKOUT_DETAILS,
} from '@guryou/redux';
import { parseAsBool } from '@guryou/utils';

const WorkoutContext = createContext();

const WorkoutProvider = ({ children, workoutId: propWorkoutId }) => {
  const dispatch = useDispatch();
  const { workoutId: urlWorkoutId } = useParams();
  const workoutId = propWorkoutId || urlWorkoutId;

  const workout = useSelector(state => state.workouts.details);
  const exercises = useSelector(state => state.exercises.all);
  const equipments = useSelector(state => state.equipments.all);

  const [ctxState, setCtxState] = useState({ isLoading: true, expanded: {} });

  useEffect(() => {
    console.log('WorkoutContext - workoutId:', workoutId);
    if (workoutId) {
      dispatch(getExercises());
      dispatch(getEquipments());
      dispatch(getWorkoutDetails(workoutId));
    }
  }, [dispatch, workoutId]);

  useEffect(() => {
    console.log('WorkoutContext loading check:', {
      equipments: equipments.status,
      workout: workout.status,
      exercises: exercises.status,
      StateStatus_LOADING: StateStatus.LOADING,
    });

    if ([equipments.status, workout.status, exercises.status].some(x => x <= StateStatus.LOADING)) {
      console.log('Still loading...');
      setCtxState(o => ({ ...o, isLoading: true }));
      return;
    }

    console.log('All loaded, setting isLoading to false');
    setCtxState(o => ({ ...o, isLoading: false, workout: workout.data }));
  }, [equipments.status, exercises.data, exercises.status, workout.data, workout.status]);

  // Circuits
  useEffectOnSuccess([state => state.workouts.circuitsCreate, state => state.workouts.circuitsUpdate], ({ id, idx, title }, request) => {
    setCtxState(o => {
      if (!o.workout.circuits) {
        o.workout.circuits = [];
      }
      if (request) {
        const index = o.workout.circuits.findIndex(x => x.id === id);
        const circuit = o.workout.circuits[index];
        o.workout.circuits[index] = { ...circuit, ...request, idx };
      } else {
        o.workout.circuits.push({ id, title, idx });
      }
      return { ...o };
    });
  });

  useEffectOnSuccess(
    state => state.workouts.circuitsDelete,
    ({ id }) => {
      setCtxState(o => {
        o.workout.circuits = o.workout.circuits.filter(x => x.id !== id);
        return { ...o };
      });
    }
  );

  useEffect(() => {
    return () => stateIsLoaded(workout) && dispatch(resetState(WORKOUT_DETAILS));
    //eslint-disable-next-line
  }, [dispatch]);

  const saveCircuit = data => {
    if (typeof data === 'string') {
      dispatch(createCircuit(workoutId, data));
      return;
    }
    dispatch(updateCircuit(data));
  };

  const removeCircuit = data => dispatch(deleteWorkoutItem(data, true));
  // Circuits - end

  // Exercise
  useEffectOnSuccess([state => state.workouts.circuitExerciseCreate, state => state.workouts.circuitExerciseUpdate], ({ id, idx }, request) => {
    setCtxState(o => {
      const circuitIndex = o.workout.circuits.findIndex(x => x.id === request.circuitId);
      const workoutItemIndex = o.workout.circuits[circuitIndex].exercises?.findIndex(x => x.id === id);

      if (workoutItemIndex > -1) {
        const workoutItem = o.workout.circuits[circuitIndex].exercises[workoutItemIndex];
        if (workoutItem.exerciseId !== request.exerciseId) {
          const newEx = { ...workoutItem, ...request };
          if (newEx.executionMode === ExerciseExecutionMode.repetition) {
            newEx.executionDisplayUnits = ExerciseDisplayUnits.times;
          }
          o.workout.circuits[circuitIndex].exercises[workoutItemIndex] = { ...newEx, idx };
          o.workout.circuits[circuitIndex].exercises[workoutItemIndex].exercise = exercises.data.find(x => x.id === request.exerciseId);
        } else {
          const newEx = { ...workoutItem, ...request };
          if (newEx.executionMode === ExerciseExecutionMode.repetition) {
            newEx.executionDisplayUnits = ExerciseDisplayUnits.times;
          }
          o.workout.circuits[circuitIndex].exercises[workoutItemIndex] = { ...newEx, idx };
        }
      } else {
        if (!o.workout.circuits[circuitIndex].exercises) {
          o.workout.circuits[circuitIndex].exercises = [];
        }
        o.workout.circuits[circuitIndex].exercises.push({ id, idx, exercise: {}, circuitId: request.circuitId });
      }

      return { ...o };
    });
  });

  useEffectOnSuccess(
    state => state.workouts.circuitExerciseDelete,
    ({ id }, request) => {
      setCtxState(o => {
        const circuitIndex = o.workout.circuits.findIndex(x => x.id === request.circuitId);
        const circuit = o.workout.circuits[circuitIndex];
        o.workout.circuits[circuitIndex].exercises = circuit.exercises.filter(x => x.id !== id);
        return { ...o };
      });
    }
  );

  const saveExercise = data => {
    if (!data.id) {
      dispatch(createCircuitExercise(data.circuitId));
      return;
    }
    dispatch(updateCircuitExercise(data));
  };

  const removeExercise = data => dispatch(deleteWorkoutItem(data, false));

  const reorderExercises = (circuitIndex, currPosition, newPosition) => {
    const itemA = ctxState.workout.circuits[circuitIndex].exercises[currPosition];
    const itemB = ctxState.workout.circuits[circuitIndex].exercises[newPosition];

    if (itemA && itemB) {
      dispatch(reorderCircuitItems({ circuitIndex, currPosition, newPosition, firstId: itemA.id, secondId: itemB.id }));
    }
  };
  const reorderCircuits = (currPosition, newPosition) => {
    const itemA = ctxState.workout.circuits[currPosition];
    const itemB = ctxState.workout.circuits[newPosition];

    if (itemA && itemB) {
      dispatch(reorderCircuitItems({ currPosition, newPosition, firstId: itemA.id, secondId: itemB.id }));
    }
  };

  useEffectOnSuccess(
    state => state.workouts.circuitReorder,
    (_, { circuitIndex, currPosition, newPosition }) => {
      setCtxState(o => {
        let itemA, itemB;

        if (circuitIndex === undefined) {
          itemA = o.workout.circuits[currPosition];
          itemB = o.workout.circuits[newPosition];
        } else {
          itemA = o.workout.circuits[circuitIndex].exercises[currPosition];
          itemB = o.workout.circuits[circuitIndex].exercises[newPosition];
        }

        if (!itemA || !itemB) {
          return o;
        }

        if (circuitIndex === undefined) {
          o.workout.circuits[currPosition] = { ...itemB, idx: itemA.idx };
          o.workout.circuits[newPosition] = { ...itemA, idx: itemB.idx };
        } else {
          o.workout.circuits[circuitIndex].exercises[currPosition] = { ...itemB, idx: itemA.idx };
          o.workout.circuits[circuitIndex].exercises[newPosition] = { ...itemA, idx: itemB.idx };
        }

        return { ...o };
      });
    }
  );
  // Exercise - end

  // Equipment
  useEffectOnSuccess(
    [state => state.workouts.circuitExerciseEquipmentCreate, state => state.workouts.circuitExerciseEquipmentUpdate],
    ({ id }, request) => {
      setCtxState(o => {
        const circuitIndex = o.workout.circuits.findIndex(x => x.id === request.circuitId);
        const exerciseIndex = o.workout.circuits[circuitIndex].exercises.findIndex(x => x.id === request.workoutCircuitId);
        const exercise = o.workout.circuits[circuitIndex].exercises[exerciseIndex].exercise;
        const equipmentIndex = exercise.equipments?.findIndex(x => x.id === id);

        if (equipmentIndex > -1) {
          const equipment = exercise.equipments[equipmentIndex];
          const { name } = equipments.data.find(x => x.id === equipment.equipmentId) || {};
          exercise.equipments[equipmentIndex] = { ...equipment, id, ...request, isDefault: false, name };
        } else {
          if (!exercise.equipments) {
            exercise.equipments = [];
          }
          exercise.equipments.push({ id, workoutCircuitId: request.workoutCircuitId, ...request, isDefault: false });
        }
        o.workout.circuits[circuitIndex].exercises[exerciseIndex].exercise = exercise;

        return { ...o };
      });
    }
  );

  useEffectOnSuccess(
    state => state.workouts.circuitExerciseEquipmentDelete,
    ({ id }, request) => {
      setCtxState(o => {
        const circuitIndex = o.workout.circuits.findIndex(x => x.id === request.circuitId);
        const exerciseIndex = o.workout.circuits[circuitIndex].exercises.findIndex(x => x.id === request.workoutCircuitId);
        const { equipments } = o.workout.circuits[circuitIndex].exercises[exerciseIndex].exercise;
        o.workout.circuits[circuitIndex].exercises[exerciseIndex].exercise.equipments = equipments.filter(x => x.id !== id);
        return { ...o };
      });
    }
  );

  const saveEquipment = data => {
    if (!data.id || parseAsBool(data.isDefault)) {
      dispatch(createCircuitExerciseEquipment(data));
      return;
    }
    dispatch(updateCircuitExerciseEquipment(data));
  };

  const removeEquipment = data => dispatch(deleteWorkoutEquipmentOverrideItem(data));
  // Equipment - end

  const toggleExpandedState = id => {
    setCtxState(o => {
      o.expanded[id] = !o.expanded[id];
      return { ...o };
    });
  };

  const filterUnitsByMode = (mode, exerciseUnits) => {
    if (!mode) {
      return exerciseUnits;
    }
    let units;
    if (mode.includes('repetition') || mode === 'laps') {
      units = exerciseUnits.filter(x => x?.value === 'times');
    }
    if (mode === 'distance') {
      units = exerciseUnits.filter(x => x?.value === 'meters' || x?.value === 'km');
    }
    if (mode.includes('duration')) {
      units = exerciseUnits.filter(x => x?.value === 'min' || x?.value === 'sec');
    }
    return units;
  };

  return (
    <WorkoutContext.Provider
      value={{
        workoutId,
        state: ctxState,
        toggleExpandedState,
        filterUnitsByMode,
        saveExercise,
        saveCircuit,
        saveEquipment,
        removeExercise,
        removeCircuit,
        removeEquipment,
        reorderExercises,
        reorderCircuits,
      }}
    >
      {children}
    </WorkoutContext.Provider>
  );
};

export { WorkoutProvider, WorkoutContext };
