import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Block, ContainedButton, DefaultText, FlexColumn, FlexRow, Modal, SearchBox, SmallText } from '@guryou/html-components';
import { borderDefinition } from '@guryou/html-theme';

const ExerciseCard = ({ exercise, onSelect, isSelected }) => {
  console.log('exercise', exercise);
  const { t } = useTranslation();

  return (
    <FlexColumn
      padding={2}
      margin={1}
      border={borderDefinition()}
      style={{
        cursor: 'pointer',
        backgroundColor: isSelected ? '#e3f2fd' : 'white',
        borderColor: isSelected ? '#2196f3' : '#e0e0e0',
        borderWidth: isSelected ? '2px' : '1px',
      }}
      onClick={() => onSelect(exercise)}
    >
      {/* Exercise GIF/Image */}
      <FlexRow justifyContent="center" marginBottom={1}>
        {exercise.gif?.thumbnailUrl || exercise.gifThumbnailUrl ? (
          <img
            src={exercise.gif?.thumbnailUrl || exercise.gifThumbnailUrl}
            alt={exercise.name}
            style={{
              width: '120px',
              height: '120px',
              objectFit: 'cover',
              borderRadius: '8px',
            }}
          />
        ) : exercise.image?.thumbnailUrl || exercise.imageThumbnailUrl ? (
          <img
            src={exercise.image?.thumbnailUrl || exercise.imageThumbnailUrl}
            alt={exercise.name}
            style={{
              width: '120px',
              height: '120px',
              objectFit: 'cover',
              borderRadius: '8px',
            }}
          />
        ) : (
          <FlexColumn
            justifyContent="center"
            alignItems="center"
            style={{
              width: '120px',
              height: '120px',
              backgroundColor: '#f5f5f5',
              borderRadius: '8px',
            }}
          >
            <SmallText color="textSecondary">{t('PROVIDER-GALLERY.insertImage')}</SmallText>
          </FlexColumn>
        )}
      </FlexRow>

      {/* Exercise Name */}
      <DefaultText textAlign="center" fontWeight="bold" marginBottom={0.5} style={{ fontSize: '14px' }}>
        {exercise.name}
      </DefaultText>

      {/* Exercise Details */}
      {exercise.muscleArea && (
        <SmallText textAlign="center" color="textSecondary">
          {exercise.muscleArea}
        </SmallText>
      )}
      {exercise.level && (
        <SmallText textAlign="center" color="textSecondary">
          {t('GENERAL.level')}: {exercise.level}
        </SmallText>
      )}
    </FlexColumn>
  );
};

const ExerciseSelectionModal = ({ modalId, onSelect, selectedExercise = null, title = null }) => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const exercisesPerPage = 12;

  const exercises = useSelector(state => state.exercises.all);

  const filteredExercises = useMemo(() => {
    if (!exercises.data) {
      return [];
    }

    return exercises.data.filter(
      exercise =>
        exercise.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (exercise.muscleArea && exercise.muscleArea.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (exercise.description && exercise.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [exercises.data, searchTerm]);

  const totalPages = Math.ceil(filteredExercises.length / exercisesPerPage);
  const startIndex = (currentPage - 1) * exercisesPerPage;
  const paginatedExercises = filteredExercises.slice(startIndex, startIndex + exercisesPerPage);

  const handleExerciseSelect = exercise => {
    onSelect(exercise);
  };

  const handleSearchChange = value => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  return (
    <Modal id={modalId} title={title || t('EXERCISE.exercises')} maxWidth="lg">
      <FlexColumn>
        <FlexRow marginBottom={2}>
          <SearchBox
            placeholder={t('GENERAL.search')}
            value={searchTerm}
            onChange={handleSearchChange}
            fullWidth
            style={{ backgroundColor: 'white' }}
          />
        </FlexRow>

        <FlexRow marginBottom={2}>
          <SmallText color="textSecondary">
            {t('EXERCISE.showingResults', {
              count: filteredExercises.length,
              total: exercises.data?.length || 0,
            })}
          </SmallText>
        </FlexRow>

        <FlexRow flexWrap="wrap" justifyContent="flex-start">
          {paginatedExercises.map(exercise => (
            <Block key={exercise.id} xs={12} sm={6} md={4} lg={3}>
              <ExerciseCard exercise={exercise} onSelect={handleExerciseSelect} isSelected={selectedExercise?.id === exercise.id} />
            </Block>
          ))}
        </FlexRow>

        {/* No Results */}
        {filteredExercises.length === 0 && (
          <FlexRow justifyContent="center" padding={4}>
            <DefaultText color="textSecondary">{t('EXERCISE.noExercisesFound')}</DefaultText>
          </FlexRow>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <FlexRow justifyContent="center" marginTop={2} gap={1}>
            <ContainedButton text={t('<')} disabled={currentPage === 1} onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))} size="small" />
            <DefaultText margin={1}>{t('COMMON.pageOfPages', { current: currentPage, total: totalPages })}</DefaultText>
            <ContainedButton
              text={t('>')}
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              size="small"
            />
          </FlexRow>
        )}
      </FlexColumn>
    </Modal>
  );
};

export default ExerciseSelectionModal;
