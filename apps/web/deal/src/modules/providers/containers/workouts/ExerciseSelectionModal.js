import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Block, ContainedButton, DefaultText, FlexColumn, FlexRow, Modal, PaperBlock, SmallText, TextBox } from '@guryou/html-components';
import { stripHtml } from '@guryou/utils';
import { Card, CardContent, CardMedia } from '@material-ui/core';

const ExerciseCard = ({ exercise, onSelect, isSelected }) => {
  const { t } = useTranslation();

  const thumbnailUrl = exercise.gif?.thumbnailUrl || exercise.gifThumbnailUrl || exercise.image?.thumbnailUrl || exercise.imageThumbnailUrl;

  return (
    <Card
      style={{
        height: '220px',
        width: '200px',
        justifyContent: 'start',
        cursor: 'pointer',
        border: isSelected ? '2px solid #2196f3' : '1px solid #e0e0e0',
        backgroundColor: isSelected ? '#e3f2fd' : 'white',
      }}
      onClick={() => onSelect(exercise)}
    >
      <CardMedia
        image={thumbnailUrl || '/placeholder-exercise.png'}
        title={exercise.name}
        style={{
          height: '140px',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      />
      <CardContent style={{ overflowY: 'scroll', display: 'block', padding: '8px' }}>
        <FlexColumn justifyContent="center" alignItems="flex-start" margin={0}>
          <DefaultText style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '4px' }}>{exercise.name}</DefaultText>
          {exercise.muscleArea && (
            <SmallText marginTop={1} color="textSecondary">
              {exercise.muscleArea}
            </SmallText>
          )}
          {exercise.level && (
            <SmallText color="textSecondary">
              {t('GENERAL.level')}: {exercise.level}
            </SmallText>
          )}
          {exercise.description && (
            <SmallText marginTop={1} color="textSecondary">
              {stripHtml(exercise.description).substring(0, 50)}...
            </SmallText>
          )}
        </FlexColumn>
      </CardContent>
    </Card>
  );
};

const ExerciseSelectionModal = ({ modalId, onSelect, selectedExercise = null, title = null }) => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const exercisesPerPage = 12;

  const exercises = useSelector(state => state.exercises.all);

  const filteredExercises = useMemo(() => {
    if (!exercises.data) {
      return [];
    }

    return exercises.data.filter(
      exercise =>
        exercise.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (exercise.muscleArea && exercise.muscleArea.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (exercise.description && exercise.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [exercises.data, searchTerm]);

  const totalPages = Math.ceil(filteredExercises.length / exercisesPerPage);
  const startIndex = (currentPage - 1) * exercisesPerPage;
  const paginatedExercises = filteredExercises.slice(startIndex, startIndex + exercisesPerPage);

  const handleExerciseSelect = exercise => {
    onSelect(exercise);
  };

  const handleSearchChange = value => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  return (
    <Modal id={modalId} title={title || t('EXERCISE.exercises')} maxWidth="lg">
      <Block>
        <PaperBlock padding={1} margin={2}>
          <TextBox placeholder={t('GENERAL.search')} value={searchTerm} onChange={handleSearchChange} />
        </PaperBlock>

        <FlexRow justifyContent="flex-start" alignItems="center" flexWrap="wrap" gap={2} key={Date.now()}>
          {paginatedExercises.map(exercise => (
            <div key={exercise.id} style={{ margin: '8px' }}>
              <ExerciseCard exercise={exercise} onSelect={handleExerciseSelect} isSelected={selectedExercise?.id === exercise.id} />
            </div>
          ))}
        </FlexRow>

        {filteredExercises.length === 0 && (
          <FlexRow justifyContent="center" padding={4}>
            <DefaultText color="textSecondary">{t('EXERCISE.noExercisesFound')}</DefaultText>
          </FlexRow>
        )}

        {totalPages > 1 && (
          <FlexRow justifyContent="center" marginTop={2} gap={1}>
            <ContainedButton text={t('<')} disabled={currentPage === 1} onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))} size="small" />
            <DefaultText margin={1}>{t('COMMON.pageOfPages', { current: currentPage, total: totalPages })}</DefaultText>
            <ContainedButton
              text={t('>')}
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              size="small"
            />
          </FlexRow>
        )}
      </Block>
    </Modal>
  );
};

export default ExerciseSelectionModal;
