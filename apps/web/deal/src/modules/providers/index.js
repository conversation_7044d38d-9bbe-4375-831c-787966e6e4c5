import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect } from 'react-router';
import { EntityTypes } from '@guryou/core';
import { useEffectOnce } from '@guryou/hooks';
import {
  CatalogoIcon,
  ClientsIcon,
  FullScreenSpinner,
  LibraryIcon,
  MyArchiveIcon,
  MyAssignmentIcon,
  MySettingsIcon,
  MySportIcon,
  OverviewIcon,
  PlayIcon,
  ProfileBusinessIcon,
  ProfileIcon,
} from '@guryou/html-components';
import {
  getProviderGeneralInformation,
  PROVIDER_GET_GENERAL_INFO,
  resetProviderState,
  resetState,
  stateIsNotInitialized,
  stateIsNotReady,
  updateUserLogin,
} from '@guryou/redux';
import { checkIfBusiness, checkIfCollaborator, checkIfManager } from '@guryou/utils';
import { persistor } from 'store';
import Module from 'components/Module';
import SubMenu from 'components/navigation/SubMenu';
import CalendarContainer from './calendar';
import ActiveMemberships from './containers/activeMemberships';
import UsersMembership from './containers/activeMemberships/UsersMembership';
import { ArchivedCourses, ArchivedEvents, ArchivedServices, ArchivedVideos } from './containers/allArchivedServices';
import Groups from './containers/allGroups';
import ArchivedCustomers from './containers/archivedCustomers';
import ArchivedMemberships from './containers/archivedMemberships';
import ArchivedPersonnel from './containers/archivedPersonnel';
import ArchivedWorkoutPlans from './containers/archivedWorkoutPlansTemplates';
import UpdateToGrow from './containers/b2bSubscriptions/UpdateToGrow';
import BoughtVideos from './containers/boughtVideos';
import UsersVideo from './containers/boughtVideos/UsersVideo';
import CollaboratorReport from './containers/collaboratorReport';
// import Broadcast from './containers/broadcast';
import CourseEventWizard from './containers/courseEventWizard';
import Courses from './containers/courses';
import Customers from './containers/customers';
import Blacklist from './containers/customers/Blacklist';
import CustomerDetails from './containers/customers/CustomerDetails';
import EmailCustomization from './containers/emailCustomization';
import Exercise from './containers/exercise';
import ExerciseForm from './containers/exercise/ExerciseForm';
import GeneralInformation from './containers/GeneralInformation';
import Holidays from './containers/holidays';
import MembershipsRules from './containers/membershipRules';
import MembershipGroups from './containers/memberships/MembershipGroups';
import NotificationsSettings from './containers/NotificationsSettings';
import OnboardingWizard from './containers/onboardingWizard';
import Overview from './containers/overview';
import PaymentSettingsContainer from './containers/paymentSettings';
import Personnel from './containers/personnel';
import PhysicalPersonnel from './containers/physicalPersonnel';
import ProviderGallery from './containers/ProviderGallery';
import AllPurchases from './containers/purchases';
import Services from './containers/services';
import ServiceWizard from './containers/serviceWizard';
import SessionWizard from './containers/sessionWizard';
import Media from './containers/sharedMedia';
import UserWorkoutPlans from './containers/userWorkoutPlans';
import UserWorkoutGroupProgressDetails from './containers/userWorkoutPlans/UserWorkoutGroupProgressDetails';
import UserWorkoutPlanEdit from './containers/userWorkoutPlans/UserWorkoutPlanEdit';
import UserWorkoutProgressDetails2 from './containers/userWorkoutPlans/UserWorkoutProgressDetails2';
import Videos from './containers/videos';
// import Wallets from './containers/wallets';
import WebIntegrations from './containers/webIntegrations';
import WorkingHours from './containers/WorkingHours';
import WorkoutPlans from './containers/workoutPlansTemplates';
import EditWorkoutPlanV2 from './containers/workoutPlansTemplates/EditWorkoutPlanV2';
import Workouts from './containers/workouts';
import EditWorkoutV2 from './containers/workouts/EditWorkoutV2';
import WorkoutManagement from './containers/workouts/WorkoutManagement';

const Providers = props => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { providerId } = props.match.params;
  const auth = useSelector(state => state.auth);
  const providerDetails = useSelector(state => state.provider.generalInfo);

  useEffectOnce(() => {
    if (auth.data.providerId !== +providerId) {
      dispatch(updateUserLogin(+auth.data.userId, +providerId, persistor));
    }
  });

  useEffect(() => {
    return () => {
      dispatch(resetState(PROVIDER_GET_GENERAL_INFO));
      dispatch(resetProviderState());
    };
  }, [dispatch]);

  useEffect(() => {
    if (+auth.data.providerId === +providerId && stateIsNotInitialized(providerDetails)) {
      dispatch(getProviderGeneralInformation());
    }
  }, [dispatch, providerId, auth, providerDetails]);

  if (!auth.persisted || +auth.data.providerId !== +providerId || stateIsNotReady([auth, providerDetails])) {
    if (providerDetails.errors?.length) {
      return <Redirect to="/management/providers" />;
    }
    return <FullScreenSpinner />;
  }

  const locked = providerDetails?.data?.locked;

  const routes = [
    {
      title: t('CALENDAR.titlePlural'),
      path: 'calendar',
      route: 'calendar',
      component: CalendarContainer,
      icon: MyAssignmentIcon,
      hidden: locked,
    },
    {
      title: t('GENERAL.dashboard').toUpperCase(),
      path: 'overview',
      route: 'overview',
      icon: OverviewIcon,
      hidden: locked,
      children: [
        {
          title: t('GENERAL.dashboard'),
          path: 'dashboard',
          route: 'dashboard',
          component: Overview,
          hidden: locked || checkIfCollaborator(),
        },
        {
          title: t('GENERAL.orders'),
          path: 'payment',
          route: 'payment',
          component: AllPurchases,
          hidden: locked,
        },
        {
          title: t('COLLABORATOR_PERCENT.forCollaborator'),
          path: 'collaborator-report',
          route: 'collaborator-report',
          component: CollaboratorReport,
          hidden: +providerDetails?.data?.id !== 260 && +providerDetails?.data?.id !== 918,
        },
      ],
    },
    {
      title: t('CUSTOMERS.titlePlural').toUpperCase(),
      path: `customers`,
      route: `customers`,
      component: Customers,
      icon: ClientsIcon,
      hidden: locked,
      children: [
        { title: t('CUSTOMERS.titlePlural'), path: `customers`, route: `customers`, component: Customers, exact: true },
        { title: t('MEMBERSHIPS.titlePlural'), path: 'customers-memberships', route: 'customers-memberships', component: ActiveMemberships },
        {
          title: t('VIDEOS.video'),
          path: 'customers-videos',
          route: 'customers-videos',
          component: BoughtVideos,
        },
        {
          exact: true,
          title: t('WORKOUTPLANS.titlePlural'),
          path: 'customers-workout-plans',
          route: 'customers-workout-plans',

          component: UserWorkoutPlans,
        },
        // {
        //   exact: true,
        //   title: t('WALLETS.titlePlural'),
        //   path: 'customers-wallets',
        //   route: 'customers-wallets',

        //   component: Wallets,
        // },
      ],
    },
    {
      title: t('GENERAL.catalogue'),
      path: 'catalogue',
      route: 'catalogue',
      icon: CatalogoIcon,
      hidden: locked,
      children: [
        {
          title: t('SERVICES.titlePlural'),
          path: 'services',
          route: 'services',
          component: Services,
        },
        {
          title: t('COURSES.titlePlural'),
          path: 'courses',
          route: 'courses',
          component: Courses,
          componentProps: { type: EntityTypes.course },
        },
        {
          title: t('EVENTS.titlePlural'),
          path: 'events',
          route: 'events',
          component: Courses,
          componentProps: { type: EntityTypes.event },
        },
        {
          title: t('VIDEOS.video'),
          path: 'videos',
          route: 'videos',
          component: Videos,
        },
        {
          exact: true,
          title: t('MEMBERSHIPS.titlePlural'),
          path: 'memberships',
          route: 'memberships',
          component: MembershipGroups,
        },
        {
          exact: true,
          title: t('WORKOUTPLANS.titlePlural'),
          path: 'workout-plans',
          route: 'workout-plans',

          component: WorkoutPlans,
          hidden: checkIfCollaborator(),
        },
      ],
    },
    {
      title: t('LIBRARY.titlePlural').toUpperCase(),
      path: 'library',
      route: 'library',
      icon: LibraryIcon,
      hidden: locked,
      children: [
        {
          title: t('MEDIA_GALLERY.title'),
          path: 'media',
          route: 'media',
          icon: PlayIcon,
          component: Media,
        },
        {
          exact: true,
          title: t('EXERCISE.titlePlural'),
          path: 'exercise',
          route: 'exercise',
          icon: MySportIcon,

          component: Exercise,
          hidden: checkIfCollaborator(),
        },
        {
          exact: true,
          title: t('WORKOUTS.titlePlural'),
          path: 'workouts',
          route: 'workouts',
          icon: MySportIcon,

          component: Workouts,
          hidden: checkIfCollaborator(),
        },
      ],
    },
    {
      title: t('RESOURCES.titlePlural').toUpperCase(),
      path: 'resources',
      route: 'resources',
      icon: ProfileIcon,
      hidden: locked,
      children: [
        { title: t('RESOURCES.titlePlural'), path: `personnel`, route: `personnel`, component: Personnel, exact: true },
        { title: t('RESOURCES.title'), path: `physical-personnel`, route: `physical-personnel`, component: PhysicalPersonnel, exact: true },
        {
          title: t('HOLIDAYS.title'),
          path: 'holidays',
          route: 'holidays',
          component: Holidays,
          hidden: checkIfCollaborator(),
        },
      ],
    },
    {
      title: t('PROFILE.titlePlural'),
      path: 'publicProfile',
      route: 'publicProfile',
      icon: ProfileBusinessIcon,
      hidden: checkIfCollaborator() || locked,
      border: true,
      children: [
        {
          title: t('BUSINESS.updateToGrow'),
          path: 'update-to-grow',
          route: 'update-to-grow',
          component: UpdateToGrow,
          hidden: checkIfManager(),
        },
        {
          title: t('PROFILE.generalInformation'),
          path: 'general-information',
          route: 'general-information',
          component: GeneralInformation,
        },

        { title: t('PROFILE.imageGallery'), path: 'profile-image-gallery', route: 'profile-image-gallery', component: ProviderGallery },
        { title: t('PROFILE.openingHours'), path: 'profile-opening-hours', route: 'profile-opening-hours', component: WorkingHours },
      ],
    },
    {
      title: t('CONFIGURATION.titlePlural'),
      path: 'configuration',
      route: 'configuration',
      icon: MySettingsIcon,
      hidden: checkIfCollaborator() || locked,
      children: [
        {
          title: t('PROFILE.payments'),
          path: 'profile-config-payment',
          route: 'profile-config-payment',
          component: PaymentSettingsContainer,
          hidden: checkIfManager(),
        },
        { title: t('GENERAL.groups'), path: 'groups', route: 'groups', component: Groups },
        { title: t('PROFILE.integrations'), path: 'web-integrations', route: 'web-integrations', component: WebIntegrations },
        {
          title: t('NOTIFICATIONS_SETTINGS.notifications'),
          path: 'notifications-settings',
          route: 'notifications-settings',
          component: NotificationsSettings,
          hidden: !checkIfBusiness(),
        },
        // {
        //   title: t('BROADCAST.title'),
        //   path: 'broadcast',
        //   route: 'broadcast',
        //   component: Broadcast,
        //   hidden: !checkIfBusiness(),
        // },
        {
          title: t('GENERAL.emailTemplates'),
          path: 'email-templates',
          route: 'email-templates',
          component: EmailCustomization,
        },
        {
          exact: true,
          title: t('MEMBERSHIP_RULES.title'),
          path: 'memberships-rules',
          route: 'memberships-rules',
          hidden: +providerDetails?.data?.id !== 260 && +providerDetails?.data?.id !== 976, // available only for Studio fitness and Passion Iron
          component: MembershipsRules,
        },
      ],
    },
    {
      title: t('GENERAL.archive').toUpperCase(),
      path: 'Archive',
      route: 'Archive',
      icon: MyArchiveIcon,
      hidden: checkIfCollaborator() || locked,
      children: [
        {
          title: t('SERVICES.titlePlural'),
          path: 'archived-services',
          route: 'archived-services',
          component: ArchivedServices,
        },
        {
          title: t('COURSES.titlePlural'),
          path: 'archived-courses',
          route: 'archived-courses',
          component: ArchivedCourses,
        },
        {
          title: t('EVENTS.titlePlural'),
          path: 'archived-events',
          route: 'archived-events',
          component: ArchivedEvents,
        },
        {
          title: t('VIDEOS.video'),
          path: 'archived-videos',
          route: 'archived-videos',
          component: ArchivedVideos,
        },
        {
          title: t('RESOURCES.titlePlural'),
          path: 'archived-personnel',
          route: 'archived-personnel',
          component: ArchivedPersonnel,
        },
        {
          title: t('MEMBERSHIPS.titlePlural'),
          path: 'archived-memberships',
          route: 'archived-memberships',
          component: ArchivedMemberships,
        },
        {
          title: t('WORKOUTPLANS.titlePlural'),
          path: 'archived-workout-plans',
          route: 'archived-workout-plans',
          component: ArchivedWorkoutPlans,
        },
        {
          title: t('ARCHIVED_CUSTOMERS.title'),
          path: 'archived-customers',
          route: 'archived-customers',
          component: ArchivedCustomers,
        },
      ],
    },

    {
      path: 'memberships/:membershipId',
      route: 'memberships/:membershipId',
      component: UsersMembership,
      hideMenuLink: true,
    },
    {
      path: 'video/:videoId',
      route: 'video/:videoId',
      component: UsersVideo,
      hideMenuLink: true,
    },
    {
      path: `customers/:identifier`,
      route: `customers/:identifier`,
      component: CustomerDetails,
      hideMenuLink: true,
    },
    {
      path: `workouts/create`,
      route: `workouts/create`,
      component: WorkoutManagement,
      hideMenuLink: true,
    },
    {
      path: `workouts/:workoutId`,
      route: `workouts/:workoutId`,
      component: WorkoutManagement,
      hideMenuLink: true,
    },
    {
      path: `exercise/create`,
      route: `exercise/create`,
      component: ExerciseForm,
      hideMenuLink: true,
    },
    {
      path: `exercise/:exerciseId`,
      route: `exercise/:exerciseId`,
      component: ExerciseForm,
      hideMenuLink: true,
    },
    {
      path: `customers-workout-plans/:userPlanId`,
      route: `customers-workout-plans/:userPlanId`,
      component: UserWorkoutPlanEdit,
      hideMenuLink: true,
    },
    {
      path: `user-progress/:userPlanId`,
      route: `user-progress/:userPlanId`,
      component: UserWorkoutProgressDetails2,
      hideMenuLink: true,
    },
    {
      path: `group-progress/:groupPlanId`,
      route: `group-progress/:groupPlanId`,
      component: UserWorkoutGroupProgressDetails,
      hideMenuLink: true,
    },
    {
      path: 'customers-blacklist',
      route: 'customers-blacklist',
      hideMenuLink: true,
      component: Blacklist,
    },
    {
      path: 'service-create',
      route: 'service-create',
      hideMenuLink: true,
      component: ServiceWizard,
    },
    {
      path: 'course-create',
      route: 'course-create',
      hideMenuLink: true,
      component: CourseEventWizard,
      componentProps: { type: EntityTypes.course },
    },
    {
      path: 'event-create',
      route: 'event-create',
      hideMenuLink: true,
      component: CourseEventWizard,
      componentProps: { type: EntityTypes.event },
    },
    {
      path: 'session-create',
      route: 'session-create',
      hideMenuLink: true,
      component: SessionWizard,
    },
    {
      path: 'onboarding',
      route: 'onboarding',
      hideMenuLink: true,
      component: OnboardingWizard,
    },
    {
      path: 'workout-plans/edit/:id',
      route: 'workout-plans/edit/:id',
      hideMenuLink: true,
      component: EditWorkoutPlanV2,
    },
    {
      path: 'workouts/edit/:workoutId',
      route: 'workouts/edit/:workoutId',
      hideMenuLink: true,
      component: EditWorkoutV2,
    },
  ];

  const subMenu = {
    component: () => <SubMenu routes={routes} rootUrl={`/providers/${providerId}`} />,
  };
  return <Module subMenu={subMenu} routes={routes} {...props} />;
};

export default Providers;
