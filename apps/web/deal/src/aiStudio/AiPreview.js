import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useLocation } from 'react-router-dom/cjs/react-router-dom.min';
import { createWorkoutPlan, getExercisesByIds } from '@guryou/redux';
import { makeStyles } from '@material-ui/core/styles';
import clsx from 'clsx';
import WorkoutScheduleCalendar from 'modules/providers/containers/workouts/WorkoutScheduleCalendar';

const drawerWidth = 240;
const useStyles = makeStyles(theme => ({
  content: {
    flexGrow: 1,
    display: 'flex',
    flexDirection: 'column',
    height: 'calc(100vh - 50px)',
    marginTop: '50px',
    padding: theme.spacing(3),
    transition: theme.transitions.create('margin', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
    marginLeft: -drawerWidth,
  },
  contentShift: {
    transition: theme.transitions.create('margin', {
      easing: theme.transitions.easing.easeOut,
      duration: theme.transitions.duration.enteringScreen,
    }),
    marginLeft: 0,
  },
  chatInput: {
    backgroundColor: '#212121',
    borderRadius: 12,
    '& .MuiOutlinedInput-root': {
      borderRadius: 12,
      color: 'whitesmoke',
      paddingRight: 8,
      '& fieldset': {
        border: 'none',
      },
      '&:hover fieldset': {
        border: 'none',
      },
      '&.Mui-focused fieldset': {
        border: 'none',
      },
    },
    '& .MuiOutlinedInput-input': {
      padding: '12px 12px',
    },
  },
}));

const AiPreview = ({ open }) => {
  const classes = useStyles();
  const history = useHistory();
  const [selectedWorkout, setSelectedWorkout] = useState(0);
  const [workoutData, setWorkoutData] = useState(null);
  const [saving, setSaving] = useState(false);
  const dispatch = useDispatch();
  const exercisesByIds = useSelector(state => state.exercises.byIds);
  const authService = useSelector(state => state.auth.service);

  const location = useLocation();
  const isTrainingPlan = location.pathname.includes('/preview/training-plan');

  useEffect(() => {
    const storedData = sessionStorage.getItem('aiWorkoutPlanData');
    if (storedData) {
      try {
        const parsedData = JSON.parse(storedData);
        setWorkoutData(parsedData.output || parsedData);
      } catch (error) {
        console.error('Error parsing workout data:', error);
      }
    }
  }, []);

  useEffect(() => {
    const groupExerciseIds = workoutData?.workouts?.flatMap(workout =>
      workout.circuits.flatMap(circuit => circuit.exercises.map(exercise => exercise.id))
    );
    dispatch(getExercisesByIds(groupExerciseIds));
  }, [dispatch, workoutData]);

  const renderCircuit = (circuit, index, workoutIndex) => {
    const isWarmUp = circuit.title?.toLowerCase().includes('warm') || circuit.exercises?.[0]?.exercise_type === 'warm-up';
    const isCooldown = circuit.title?.toLowerCase().includes('cool') || circuit.exercises?.[0]?.exercise_type === 'cooldown';

    const borderColor = isWarmUp ? '#e6616a' : isCooldown ? '#4a90e2' : '#469b56';
    const badgeText = isWarmUp ? 'Warm up' : isCooldown ? 'Cool down' : 'Training';
    const badgeColor = isWarmUp ? '#e6616a' : isCooldown ? '#4a90e2' : '#469b56';

    const getExerciseImage = exerciseId => {
      const exercise = exercisesByIds.data?.find(x => x.id === exerciseId);
      if (exercise?.gif) {
        return exercise.gif.thumbnailUrl;
      }
      if (exercise?.image) {
        return exercise.image.thumbnailUrl;
      }
      return null;
    };

    const getExerciseName = exerciseId => {
      const exercise = exercisesByIds.data?.find(x => x.id === exerciseId);
      return exercise?.name;
    };

    return (
      <div
        key={`circuit-${workoutIndex}-${index}`}
        style={{
          marginBottom: '20px',
          borderLeft: `4px solid ${borderColor}`,
          padding: '10px',
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h3 style={{ color: '#ccc' }}>{circuit.title || `Circuit ${index + 1}`}</h3>
          <p style={{ color: '#ccc', fontSize: '1rem' }}>{circuit.description?.split(',')[0] || `${circuit.exercises?.length || 1} exercises`}</p>
          <div
            style={{ backgroundColor: badgeColor, color: 'white', padding: '5px 15px', borderRadius: '999px', fontSize: '1rem', fontWeight: '800' }}
          >
            {badgeText}
          </div>
        </div>

        {circuit.exercises?.map((exercise, exIndex) => (
          <div key={`exercise-${workoutIndex}-${index}-${exIndex}`} style={{ display: 'flex', marginTop: '10px', alignItems: 'center' }}>
            <div
              style={{
                width: '250px',
                height: '150px',
                padding: '5px',
                borderRadius: '5px',
                marginRight: '20px',
                overflow: 'hidden',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <img
                src={getExerciseImage(exercise.id)}
                alt="Exercise"
                style={{
                  maxWidth: '100%',
                  maxHeight: '150px',
                  objectFit: 'contain',
                }}
              />
            </div>
            <div style={{ flex: 1 }}>
              <h3 style={{ color: '#ccc' }}>{getExerciseName(exercise.id)}</h3>
              <p style={{ color: '#ccc', fontSize: '1rem' }}>
                {exercise.sets || 1} {exercise.sets === 1 ? 'set' : 'sets'} of{' '}
                {exercise.duration ? `${exercise.duration} seconds` : `${exercise.min_reps} - ${exercise.max_reps} times`}
              </p>
            </div>
            {exercise.rest > 0 && (
              <div>
                <p style={{ color: '#ccc', fontSize: '1rem' }}>Rest: ({exercise.rest} sec)</p>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  const generateWorkoutContent = (workout, index) => {
    if (!workout) {
      return null;
    }

    const workoutSchedule = convertScheduleFormat(workoutData?.workouts_schedule, index);

    return (
      <>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px', gap: '20px' }}>
          <div style={{ width: '60%', marginLeft: '12px' }}>
            <h3 style={{ color: '#ccc' }}>{workout.title || `Workout ${index + 1}`}</h3>
            <p style={{ color: '#ccc', fontSize: '1rem' }}>{workout.description || 'This workout focuses on building strength and endurance.'}</p>
          </div>
          {isTrainingPlan && (
            <div style={{ color: 'black' }}>
              <WorkoutScheduleCalendar hideEdit={true} workoutSchedule={workoutSchedule} />
            </div>
          )}
        </div>

        {workout.circuits?.map((circuit, circuitIndex) => renderCircuit(circuit, circuitIndex, index))}
      </>
    );
  };

  const convertScheduleFormat = (scheduleData, workoutIndex = null) => {
    if (!scheduleData || !Array.isArray(scheduleData)) {
      return {};
    }

    const dayMap = {
      MON: 1,
      TUE: 2,
      WED: 3,
      THU: 4,
      FRI: 5,
      SAT: 6,
      SUN: 7,
      Mon: 1,
      Tue: 2,
      Wed: 3,
      Thu: 4,
      Fri: 5,
      Sat: 6,
      Sun: 7,
    };

    const result = {};

    if (workoutIndex !== null) {
      const workout = scheduleData[workoutIndex];
      if (!workout) {
        return {};
      }

      workout.weeks_schedule.forEach((days, weekIndex) => {
        const weekNum = weekIndex + 1;
        result[weekNum] = days.map(day => dayMap[day]).filter(Boolean);
      });
    } else {
      scheduleData.forEach(workout => {
        workout.weeks_schedule.forEach((days, weekIndex) => {
          const weekNum = weekIndex + 1;
          if (!result[weekNum]) {
            result[weekNum] = [];
          }

          const mappedDays = days.map(day => dayMap[day]).filter(Boolean);
          result[weekNum] = [...new Set([...result[weekNum], ...mappedDays])];
        });
      });
    }

    return result;
  };

  const handleSaveAndEdit = () => {
    if (!workoutData) {
      return;
    }

    setSaving(true);

    const planData = workoutData.output || workoutData;
    const trainingPlan = planData.training_plan;
    const workouts = planData.workouts;
    const workoutsSchedule = planData.workouts_schedule;

    const formattedData = {
      title: trainingPlan.title,
      description: trainingPlan.description,
      numberOfWeeks: trainingPlan.weeks,
      price: 0,
      isPublic: 'Y',
      workouts: workouts.map((workout, index) => {
        const workoutSchedule = workoutsSchedule.find(ws => ws.workout_id === workout.id);

        const scheduleObj = {};
        if (workoutSchedule) {
          workoutSchedule.weeks_schedule.forEach((days, weekIndex) => {
            const weekNum = weekIndex + 1;
            const dayMap = {
              MON: 1,
              Mon: 1,
              mon: 1,
              TUE: 2,
              Tue: 2,
              tue: 2,
              WED: 3,
              Wed: 3,
              wed: 3,
              THU: 4,
              Thu: 4,
              thu: 4,
              FRI: 5,
              Fri: 5,
              fri: 5,
              SAT: 6,
              Sat: 6,
              sat: 6,
              SUN: 7,
              Sun: 7,
              sun: 7,
            };
            scheduleObj[weekNum] = days.map(day => dayMap[day]).filter(Boolean);
          });
        }
        return {
          title: workout.title,
          description: workout.description,
          workoutOrder: index + 1,
          workoutSchedule: scheduleObj,
          circuits: workout.circuits.map((circuit, circuitIndex) => ({
            title: circuit.title,
            description: circuit.description,
            circuitOrder: circuitIndex + 1,
            exercises: circuit.exercises.map((exercise, exerciseIndex) => ({
              exerciseId: exercise.id,
              sets: exercise.sets,
              minReps: exercise.min_reps,
              maxReps: exercise.max_reps,
              rest: exercise.rest,
              duration: exercise.duration || 0,
              exerciseOrder: exerciseIndex + 1,
            })),
          })),
        };
      }),
    };

    dispatch(createWorkoutPlan(formattedData))
      .then(response => {
        setSaving(false);
        if (response && response.id) {
          history.push(`/providers/${authService.providerId}/workout-plans/edit/${response.id}`);
        } else {
          console.error('Failed to create workout plan');
        }
      })
      .catch(error => {
        setSaving(false);
        console.error('Error creating workout plan:', error);
      });
  };

  return (
    <main
      className={clsx(classes.content, {
        [classes.contentShift]: open,
      })}
    >
      <div style={{ color: '#ccc', padding: '20px', height: '100%', overflow: 'auto' }}>
        {isTrainingPlan && workoutData && (
          <div style={{ display: 'flex', marginBottom: '20px' }}>
            <div style={{ flex: 1, marginRight: '10px', backgroundColor: '#181818', padding: '10px', borderRadius: '5px' }}>
              <div style={{ padding: '10px' }}>
                <h2 style={{ color: '#ccc' }}>{workoutData?.training_plan?.title || '01: Core Strengthening Workout Plan for Women (50+)'}</h2>
              </div>
            </div>
            <div style={{ width: '300px', backgroundColor: '#181818', padding: '20px', borderRadius: '5px' }}>
              <h3 style={{ color: '#ccc' }}>Duration: {workoutData?.training_plan?.weeks || '3'} weeks</h3>
            </div>
          </div>
        )}

        {isTrainingPlan && workoutData && (
          <div style={{ display: 'flex', marginBottom: '20px' }}>
            <div
              style={{
                flex: 1,
                backgroundColor: '#181818',
                padding: '20px',
                borderRadius: '5px',
                display: 'flex',
                flexDirection: 'row',
              }}
            >
              <div style={{ flex: 1, marginRight: '20px' }}>
                <h3 style={{ color: '#ccc' }}>Description:</h3>
                <p style={{ color: '#ccc', fontSize: '1rem' }}>
                  {workoutData?.training_plan?.description ||
                    'This 3-week workout plan is designed to strengthen your core muscles, improve stability, and enhance overall fitness. Remember to consult your doctor before starting any new exercise program. Focus on controlled movements and proper form to prevent injuries. Listen to your body and rest when needed.'}
                </p>
              </div>
              <div style={{ flex: 1, color: 'black' }}>
                <WorkoutScheduleCalendar hideEdit={true} workoutSchedule={convertScheduleFormat(workoutData?.workouts_schedule)} />
              </div>
            </div>
          </div>
        )}

        <div style={{ display: 'flex' }}>
          <div style={{ width: '200px', marginRight: '10px' }}>
            {workoutData?.workouts?.map((workout, index) => (
              <div
                key={index}
                style={{
                  backgroundColor: '#181818',
                  padding: '10px',
                  marginBottom: '10px',
                  borderLeft: index === selectedWorkout ? '4px solid #e6616a' : '4px solid #181818',
                  borderRadius: '5px',
                  borderTopLeftRadius: '0px',
                  borderBottomLeftRadius: '0px',
                  cursor: 'pointer',
                }}
                onClick={() => setSelectedWorkout(index)}
              >
                <p style={{ color: 'white', fontSize: '1rem' }}>{workout.title || `Workout ${index + 1}`}</p>
              </div>
            ))}
          </div>

          <div style={{ flex: 1, backgroundColor: '#181818', padding: '10px', borderRadius: '5px' }}>
            {workoutData?.workouts && workoutData.workouts.length > 0 ? (
              generateWorkoutContent(workoutData.workouts[selectedWorkout], selectedWorkout)
            ) : (
              <p style={{ color: '#ccc', textAlign: 'center' }}>No workout data available</p>
            )}
          </div>
        </div>

        <div style={{ display: 'flex', justifyContent: 'center', marginTop: '20px' }}>
          <button
            style={{
              borderRadius: '999px',
              padding: '10px 20px',
              color: 'white',
              backgroundColor: '#e6616a',
              border: '1px solid #e6616a',
              fontSize: '1rem',
              fontWeight: 'bold',
              cursor: saving ? 'default' : 'pointer',
              opacity: saving ? 0.7 : 1,
            }}
            onClick={handleSaveAndEdit}
            disabled={saving}
          >
            {saving ? 'Saving...' : 'Save and Edit'}
          </button>
        </div>
      </div>
    </main>
  );
};

export default AiPreview;
